package com.flutterup.app.design.text

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.TextUnit

/**
 * AnnotatedString 工具类
 * 提供常用的 AnnotatedString 操作和处理方法
 */
object AnnotatedStringUtils {

    /**
     * 处理点击事件的标签类型
     */
    object ClickTags {
        const val URL = "URL"
        const val EMAIL = "EMAIL"
        const val PHONE = "PHONE"
        const val ACTION = "ACTION"
        const val USER = "USER"
        const val HASHTAG = "HASHTAG"
    }

    /**
     * 从 AnnotatedString 中获取指定位置的注解
     */
    fun getAnnotationAt(
        annotatedString: AnnotatedString,
        offset: Int,
        tag: String = "TAG"
    ): String? {
        return annotatedString.getStringAnnotations(
            tag = tag,
            start = offset,
            end = offset
        ).firstOrNull()?.item
    }

    /**
     * 获取所有指定标签的注解
     */
    fun getAllAnnotations(
        annotatedString: AnnotatedString,
        tag: String = "TAG"
    ): List<AnnotatedString.Range<String>> {
        return annotatedString.getStringAnnotations(tag)
    }

    /**
     * 创建带链接的文本
     */
    fun createLinkText(
        text: String,
        links: List<LinkInfo>,
        defaultColor: Color = Color.Black,
        linkColor: Color = Color.Blue
    ): AnnotatedString {
        return buildAnnotatedString {
            var lastIndex = 0
            
            links.sortedBy { it.start }.forEach { link ->
                // 添加链接前的普通文本
                if (link.start > lastIndex) {
                    withStyle(SpanStyle(color = defaultColor)) {
                        append(text.substring(lastIndex, link.start))
                    }
                }
                
                // 添加链接文本
                withStyle(
                    SpanStyle(
                        color = linkColor,
                        textDecoration = TextDecoration.Underline,
                        fontWeight = FontWeight.Medium
                    )
                ) {
                    append(text.substring(link.start, link.end))
                }
                
                // 添加注解
                addStringAnnotation(
                    tag = ClickTags.URL,
                    annotation = link.url,
                    start = link.start,
                    end = link.end
                )
                
                lastIndex = link.end
            }
            
            // 添加剩余的普通文本
            if (lastIndex < text.length) {
                withStyle(SpanStyle(color = defaultColor)) {
                    append(text.substring(lastIndex))
                }
            }
        }
    }

    /**
     * 创建带提及用户的文本
     */
    fun createMentionText(
        text: String,
        mentions: List<MentionInfo>,
        defaultColor: Color = Color.Black,
        mentionColor: Color = Color.Blue
    ): AnnotatedString {
        return buildAnnotatedString {
            var lastIndex = 0
            
            mentions.sortedBy { it.start }.forEach { mention ->
                // 添加提及前的普通文本
                if (mention.start > lastIndex) {
                    withStyle(SpanStyle(color = defaultColor)) {
                        append(text.substring(lastIndex, mention.start))
                    }
                }
                
                // 添加提及文本
                withStyle(
                    SpanStyle(
                        color = mentionColor,
                        fontWeight = FontWeight.Medium
                    )
                ) {
                    append(text.substring(mention.start, mention.end))
                }
                
                // 添加注解
                addStringAnnotation(
                    tag = ClickTags.USER,
                    annotation = mention.userId,
                    start = mention.start,
                    end = mention.end
                )
                
                lastIndex = mention.end
            }
            
            // 添加剩余的普通文本
            if (lastIndex < text.length) {
                withStyle(SpanStyle(color = defaultColor)) {
                    append(text.substring(lastIndex))
                }
            }
        }
    }

    /**
     * 创建带话题标签的文本
     */
    fun createHashtagText(
        text: String,
        hashtags: List<HashtagInfo>,
        defaultColor: Color = Color.Black,
        hashtagColor: Color = Color(0xFF1DA1F2) // Twitter 蓝
    ): AnnotatedString {
        return buildAnnotatedString {
            var lastIndex = 0
            
            hashtags.sortedBy { it.start }.forEach { hashtag ->
                // 添加话题标签前的普通文本
                if (hashtag.start > lastIndex) {
                    withStyle(SpanStyle(color = defaultColor)) {
                        append(text.substring(lastIndex, hashtag.start))
                    }
                }
                
                // 添加话题标签文本
                withStyle(
                    SpanStyle(
                        color = hashtagColor,
                        fontWeight = FontWeight.Medium
                    )
                ) {
                    append(text.substring(hashtag.start, hashtag.end))
                }
                
                // 添加注解
                addStringAnnotation(
                    tag = ClickTags.HASHTAG,
                    annotation = hashtag.tag,
                    start = hashtag.start,
                    end = hashtag.end
                )
                
                lastIndex = hashtag.end
            }
            
            // 添加剩余的普通文本
            if (lastIndex < text.length) {
                withStyle(SpanStyle(color = defaultColor)) {
                    append(text.substring(lastIndex))
                }
            }
        }
    }

    /**
     * 自动检测并创建链接文本
     */
    fun autoLinkText(
        text: String,
        defaultColor: Color = Color.Black,
        linkColor: Color = Color.Blue
    ): AnnotatedString {
        val urlPattern = Regex(
            """https?://[^\s]+|www\.[^\s]+"""
        )
        
        val matches = urlPattern.findAll(text).toList()
        val links = matches.map { match ->
            LinkInfo(
                start = match.range.first,
                end = match.range.last + 1,
                url = match.value
            )
        }
        
        return createLinkText(text, links, defaultColor, linkColor)
    }

    /**
     * 自动检测并创建邮箱链接
     */
    fun autoEmailText(
        text: String,
        defaultColor: Color = Color.Black,
        emailColor: Color = Color.Blue
    ): AnnotatedString {
        val emailPattern = Regex(
            """[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"""
        )
        
        return buildAnnotatedString {
            var lastIndex = 0
            
            emailPattern.findAll(text).forEach { match ->
                // 添加邮箱前的普通文本
                if (match.range.first > lastIndex) {
                    withStyle(SpanStyle(color = defaultColor)) {
                        append(text.substring(lastIndex, match.range.first))
                    }
                }
                
                // 添加邮箱文本
                withStyle(
                    SpanStyle(
                        color = emailColor,
                        textDecoration = TextDecoration.Underline
                    )
                ) {
                    append(match.value)
                }
                
                // 添加注解
                addStringAnnotation(
                    tag = ClickTags.EMAIL,
                    annotation = match.value,
                    start = match.range.first,
                    end = match.range.last + 1
                )
                
                lastIndex = match.range.last + 1
            }
            
            // 添加剩余的普通文本
            if (lastIndex < text.length) {
                withStyle(SpanStyle(color = defaultColor)) {
                    append(text.substring(lastIndex))
                }
            }
        }
    }

    /**
     * 处理点击事件的通用方法
     */
    fun handleClick(
        annotatedString: AnnotatedString,
        offset: Int,
        onUrlClick: ((String) -> Unit)? = null,
        onEmailClick: ((String) -> Unit)? = null,
        onPhoneClick: ((String) -> Unit)? = null,
        onUserClick: ((String) -> Unit)? = null,
        onHashtagClick: ((String) -> Unit)? = null,
        onActionClick: ((String) -> Unit)? = null
    ) {
        // 检查 URL 点击
        getAnnotationAt(annotatedString, offset, ClickTags.URL)?.let { url ->
            onUrlClick?.invoke(url)
            return
        }
        
        // 检查邮箱点击
        getAnnotationAt(annotatedString, offset, ClickTags.EMAIL)?.let { email ->
            onEmailClick?.invoke(email)
            return
        }
        
        // 检查电话点击
        getAnnotationAt(annotatedString, offset, ClickTags.PHONE)?.let { phone ->
            onPhoneClick?.invoke(phone)
            return
        }
        
        // 检查用户点击
        getAnnotationAt(annotatedString, offset, ClickTags.USER)?.let { userId ->
            onUserClick?.invoke(userId)
            return
        }
        
        // 检查话题标签点击
        getAnnotationAt(annotatedString, offset, ClickTags.HASHTAG)?.let { hashtag ->
            onHashtagClick?.invoke(hashtag)
            return
        }
        
        // 检查自定义动作点击
        getAnnotationAt(annotatedString, offset, ClickTags.ACTION)?.let { action ->
            onActionClick?.invoke(action)
            return
        }
    }
}

/**
 * 链接信息数据类
 */
data class LinkInfo(
    val start: Int,
    val end: Int,
    val url: String
)

/**
 * 提及信息数据类
 */
data class MentionInfo(
    val start: Int,
    val end: Int,
    val userId: String,
    val displayName: String? = null
)

/**
 * 话题标签信息数据类
 */
data class HashtagInfo(
    val start: Int,
    val end: Int,
    val tag: String
)
