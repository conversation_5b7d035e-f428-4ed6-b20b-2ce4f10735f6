package com.flutterup.app.design.text

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.design.theme.AppTheme

/**
 * AnnotatedString 使用示例
 */

/**
 * 基础 AnnotatedString 示例
 */
@Composable
fun BasicAnnotatedStringExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            TitleText("基础 AnnotatedString 示例")
            
            // 使用构建器创建富文本
            val richText = buildAppAnnotatedString {
                append("这是一段 ")
                appendBold("粗体文字")
                append("，这是 ")
                appendItalic("斜体文字")
                append("，这是 ")
                appendUnderline("下划线文字")
                append("。")
            }
            
            BodyText(text = richText)
            
            // 带颜色的文本
            val coloredText = buildAppAnnotatedString {
                append("这里有不同颜色的文字：")
                appendLine()
                appendError("错误文字")
                append(" | ")
                appendSuccess("成功文字")
                append(" | ")
                appendWarning("警告文字")
                append(" | ")
                appendInfo("信息文字")
            }
            
            BodyText(text = coloredText)
        }
    }
}

/**
 * 链接文本示例
 */
@Composable
fun LinkTextExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            TitleText("链接文本示例")
            
            val linkText = buildAppAnnotatedString {
                append("请阅读我们的 ")
                appendLink("用户协议", "https://example.com/terms")
                append(" 和 ")
                appendLink("隐私政策", "https://example.com/privacy")
                append("。")
            }
            
            BodyText(
                text = linkText,
                onClick = { offset ->
                    // 处理点击事件
                    // 可以通过 offset 获取点击位置的注解
                }
            )
            
            HintText("点击链接文字可以触发相应的操作")
        }
    }
}

/**
 * 主题化文本示例
 */
@Composable
fun ThemedAnnotatedStringExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            TitleText("主题化文本示例")
            
            val themedText = buildThemedAnnotatedString {
                append("欢迎使用 ")
                appendPrimary("FlutterUp", "app_name")
                append(" 应用！")
                appendLine()
                appendSecondary("这是一个功能强大的移动应用。")
                appendLine()
                appendError("注意：请确保网络连接正常。")
            }
            
            BodyText(
                text = themedText,
                onClick = { offset ->
                    // 处理主题化文本的点击
                }
            )
        }
    }
}

/**
 * 复杂格式示例
 */
@Composable
fun ComplexFormattingExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            TitleText("复杂格式示例")
            
            val complexText = buildAppAnnotatedString {
                appendStyled(
                    text = "重要通知",
                    color = Color.Red,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                appendLine()
                appendLine()
                
                append("亲爱的用户，")
                appendLine()
                
                append("我们很高兴地宣布，")
                appendEmphasis("FlutterUp v2.0")
                append(" 现已发布！新版本包含以下特性：")
                appendLine()
                appendLine()
                
                append("• ")
                appendBold("全新的用户界面")
                appendLine()
                
                append("• ")
                appendStyled(
                    text = "性能提升 50%",
                    color = Color(0xFF4CAF50),
                    fontWeight = FontWeight.Medium
                )
                appendLine()
                
                append("• ")
                appendItalic("更好的用户体验")
                appendLine()
                appendLine()
                
                append("立即 ")
                appendLink("下载更新", "https://example.com/download")
                append(" 体验新功能！")
                appendLine()
                appendLine()
                
                appendStyled(
                    text = "感谢您的支持！",
                    fontStyle = FontStyle.Italic,
                    color = Color.Gray
                )
            }
            
            BodyText(
                text = complexText,
                onClick = { offset ->
                    // 处理复杂文本的点击
                }
            )
        }
    }
}

/**
 * 表单验证示例
 */
@Composable
fun FormValidationExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            TitleText("表单验证示例")
            
            // 密码要求说明
            val passwordRequirements = buildAppAnnotatedString {
                append("密码必须包含：")
                appendLine()
                appendSuccess("✓ 至少 8 个字符")
                appendLine()
                appendError("✗ 至少一个大写字母")
                appendLine()
                appendSuccess("✓ 至少一个小写字母")
                appendLine()
                appendError("✗ 至少一个数字")
                appendLine()
                appendWarning("! 至少一个特殊字符")
            }
            
            HintText(text = passwordRequirements)
            
            // 错误提示
            val errorMessage = buildAppAnnotatedString {
                appendError("错误：")
                append("邮箱格式不正确。请输入有效的邮箱地址，例如：")
                appendEmphasis("<EMAIL>")
            }
            
            ErrorText(text = errorMessage)
        }
    }
}

/**
 * 聊天消息示例
 */
@Composable
fun ChatMessageExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            TitleText("聊天消息示例")
            
            val chatMessage = buildAppAnnotatedString {
                appendStyled(
                    text = "张三",
                    color = Color(0xFF2196F3),
                    fontWeight = FontWeight.Bold
                )
                append(" ")
                appendStyled(
                    text = "10:30",
                    color = Color.Gray,
                    fontSize = 12.sp
                )
                appendLine()
                
                append("大家好！我刚刚发现了一个很棒的 ")
                appendLink("开源项目", "https://github.com/example/project")
                append("，推荐给大家看看 ")
                appendStyled(
                    text = "😊",
                    fontSize = 16.sp
                )
            }
            
            BodyText(
                text = chatMessage,
                onClick = { offset ->
                    // 处理聊天消息点击
                }
            )
        }
    }
}

@Preview(showBackground = true, name = "AnnotatedString 示例")
@Composable
private fun AnnotatedStringExamplesPreview() {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            PageTitle("AnnotatedString 使用示例")
            
            BasicAnnotatedStringExample()
            LinkTextExample()
            ThemedAnnotatedStringExample()
            ComplexFormattingExample()
            FormValidationExample()
            ChatMessageExample()
        }
    }
}
