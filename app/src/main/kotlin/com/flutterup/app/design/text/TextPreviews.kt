package com.flutterup.app.design.text

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.design.theme.AppTheme

/**
 * 文字组件预览
 */
@Preview(showBackground = true, name = "文字组件预览")
@Composable
private fun TextComponentsPreview() {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Display 文字
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TitleText("Display 文字", size = TitleSize.Small)
                    DisplayText("Display Large", size = DisplaySize.Large)
                    DisplayText("Display Medium", size = DisplaySize.Medium)
                    DisplayText("Display Small", size = DisplaySize.Small)
                }
            }

            // Headline 文字
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TitleText("Headline 文字", size = TitleSize.Small)
                    HeadlineText("Headline Large", size = HeadlineSize.Large)
                    HeadlineText("Headline Medium", size = HeadlineSize.Medium)
                    HeadlineText("Headline Small", size = HeadlineSize.Small)
                }
            }

            // Title 文字
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TitleText("Title 文字", size = TitleSize.Small)
                    TitleText("Title Large", size = TitleSize.Large)
                    TitleText("Title Medium", size = TitleSize.Medium)
                    TitleText("Title Small", size = TitleSize.Small)
                }
            }

            // Body 文字
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TitleText("Body 文字", size = TitleSize.Small)
                    BodyText("Body Large - 这是正文大号文字，适用于重要内容的展示。", size = BodySize.Large)
                    BodyText("Body Medium - 这是正文中号文字，适用于一般内容的展示。", size = BodySize.Medium)
                    BodyText("Body Small - 这是正文小号文字，适用于次要内容的展示。", size = BodySize.Small)
                }
            }

            // Label 文字
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TitleText("Label 文字", size = TitleSize.Small)
                    LabelText("Label Large", size = LabelSize.Large)
                    LabelText("Label Medium", size = LabelSize.Medium)
                    LabelText("Label Small", size = LabelSize.Small)
                }
            }

            // 特殊组件
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TitleText("特殊文字组件", size = TitleSize.Small)
                    PageTitle("页面标题")
                    CardTitle("卡片标题")
                    CardSubtitle("卡片副标题")
                    ButtonText("按钮文字")
                    NavigationText("导航文字")
                    TagText("标签")
                    ErrorText("错误提示文字")
                    HintText("提示文字")
                    PlaceholderText("占位符文字")
                    EmphasisText("强调文字")
                    LinkText("链接文字")
                    NumberText("12345", size = NumberSize.Medium)
                }
            }
        }
    }
}

@Preview(showBackground = true, name = "文字颜色预览")
@Composable
private fun TextColorsPreview() {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            TitleText("文字颜色预览", size = TitleSize.Medium)
            
            BodyText("Primary Color", color = TextColors.primary())
            BodyText("Secondary Color", color = TextColors.secondary())
            BodyText("On Surface", color = TextColors.onSurface())
            BodyText("On Surface Variant", color = TextColors.onSurfaceVariant())
            BodyText("Error Color", color = TextColors.error())
            BodyText("Success Color", color = TextColors.success())
            BodyText("Warning Color", color = TextColors.warning())
            BodyText("Info Color", color = TextColors.info())
            BodyText("Disabled Color", color = TextColors.disabled())
            BodyText("Placeholder Color", color = TextColors.placeholder())
        }
    }
}

@Preview(showBackground = true, name = "文字样式预设预览")
@Composable
private fun TextPresetsPreview() {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    AppText("页面标题", style = TextPresets.pageTitle())
                    AppText("页面副标题", style = TextPresets.pageSubtitle())
                    AppText("卡片标题", style = TextPresets.cardTitle())
                    AppText("卡片内容", style = TextPresets.cardContent())
                    AppText("按钮文字", style = TextPresets.button())
                    AppText("输入框标签", style = TextPresets.inputLabel())
                    AppText("输入框提示", style = TextPresets.inputHint())
                    AppText("错误提示", style = TextPresets.errorMessage())
                    AppText("链接文字", style = TextPresets.link())
                    AppText("标签文字", style = TextPresets.tag())
                    AppText("导航文字", style = TextPresets.navigation())
                }
            }
        }
    }
}

@Preview(showBackground = true, name = "实际使用场景预览")
@Composable
private fun RealWorldUsagePreview() {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 模拟登录页面
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    PageTitle("欢迎回来")
                    CardSubtitle("请输入您的账号信息")
                    
                    Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                        AppText("邮箱", style = TextPresets.inputLabel())
                        PlaceholderText("请输入邮箱地址")
                    }
                    
                    Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                        AppText("密码", style = TextPresets.inputLabel())
                        PlaceholderText("请输入密码")
                        ErrorText("密码不能为空")
                    }
                    
                    ButtonText("登录")
                    LinkText("忘记密码？")
                }
            }

            // 模拟卡片列表
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    CardTitle("文章标题")
                    CardSubtitle("这是文章的简介内容，用来描述文章的主要内容...")
                    
                    Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                        HintText("发布时间：2024-01-01")
                        EmphasisText("阅读量：1,234")
                    }
                }
            }
        }
    }
}
