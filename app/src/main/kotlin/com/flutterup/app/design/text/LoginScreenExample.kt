package com.flutterup.app.design.text

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.component.AppRadioButton
import com.flutterup.app.design.theme.AppTheme

/**
 * 使用 AnnotatedString 的登录页面示例
 * 展示如何在实际场景中使用富文本功能
 */
@Composable
fun LoginScreenWithAnnotatedString(
    modifier: Modifier = Modifier,
    onTermsClick: () -> Unit = {},
    onPrivacyClick: () -> Unit = {},
    onForgotPasswordClick: () -> Unit = {},
    onRegisterClick: () -> Unit = {},
) {
    var isAgreeSelected by remember { mutableStateOf(false) }
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var hasEmailError by remember { mutableStateOf(false) }
    var hasPasswordError by remember { mutableStateOf(false) }

    Box(
        modifier = modifier.fillMaxSize()
    ) {
        Image(
            painter = painterResource(id = R.mipmap.ic_login_background),
            contentDescription = null,
            modifier = Modifier.fillMaxSize()
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 登录卡片
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 欢迎标题 - 使用 AnnotatedString
                    val welcomeText = buildAppAnnotatedString {
                        append("欢迎回来")
                        appendLine()
                        appendStyled(
                            text = "FlutterUp",
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                        )
                    }
                    
                    PageTitle(
                        text = welcomeText,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    // 副标题
                    val subtitleText = buildThemedAnnotatedString {
                        append("请输入您的账号信息")
                        appendSecondary("登录到您的账户")
                    }
                    
                    CardSubtitle(
                        text = subtitleText,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 邮箱输入
                    Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                        AppText(
                            text = "邮箱",
                            style = TextPresets.inputLabel()
                        )
                        PlaceholderText("请输入邮箱地址")
                        
                        if (hasEmailError) {
                            val emailErrorText = buildAppAnnotatedString {
                                appendError("错误：")
                                append("请输入有效的邮箱地址，例如：")
                                appendEmphasis("<EMAIL>")
                            }
                            ErrorText(text = emailErrorText)
                        }
                    }
                    
                    // 密码输入
                    Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                        AppText(
                            text = "密码",
                            style = TextPresets.inputLabel()
                        )
                        PlaceholderText("请输入密码")
                        
                        if (hasPasswordError) {
                            val passwordErrorText = buildAppAnnotatedString {
                                appendError("密码要求：")
                                appendLine()
                                appendSuccess("✓ 至少 8 个字符")
                                appendLine()
                                appendError("✗ 至少一个大写字母")
                                appendLine()
                                appendWarning("! 至少一个数字")
                            }
                            ErrorText(text = passwordErrorText)
                        }
                    }
                    
                    // 忘记密码链接
                    val forgotPasswordText = buildAppAnnotatedString {
                        appendLink("忘记密码？", "forgot_password")
                    }
                    
                    LinkText(
                        text = forgotPasswordText,
                        modifier = Modifier.align(Alignment.End),
                        onClick = { offset ->
                            AnnotatedStringUtils.handleClick(
                                annotatedString = forgotPasswordText,
                                offset = offset,
                                onActionClick = { action ->
                                    if (action == "forgot_password") {
                                        onForgotPasswordClick()
                                    }
                                }
                            )
                        }
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 登录按钮
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        ButtonText(
                            text = "登录",
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            textAlign = TextAlign.Center,
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                    
                    // 用户协议
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        AppRadioButton(
                            selected = isAgreeSelected,
                            onClick = { isAgreeSelected = !isAgreeSelected },
                            modifier = Modifier.size(16.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        val agreementText = buildAppAnnotatedString {
                            append("我已阅读并同意 ")
                            appendLink("用户协议", "terms")
                            append(" 和 ")
                            appendLink("隐私政策", "privacy")
                        }
                        
                        BodyText(
                            text = agreementText,
                            modifier = Modifier.weight(1f),
                            onClick = { offset ->
                                AnnotatedStringUtils.handleClick(
                                    annotatedString = agreementText,
                                    offset = offset,
                                    onActionClick = { action ->
                                        when (action) {
                                            "terms" -> onTermsClick()
                                            "privacy" -> onPrivacyClick()
                                        }
                                    }
                                )
                            }
                        )
                    }
                    
                    // 注册提示
                    val registerText = buildAppAnnotatedString {
                        append("还没有账号？")
                        appendLink("立即注册", "register")
                    }
                    
                    HintText(
                        text = registerText,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth(),
                        onClick = { offset ->
                            AnnotatedStringUtils.handleClick(
                                annotatedString = registerText,
                                offset = offset,
                                onActionClick = { action ->
                                    if (action == "register") {
                                        onRegisterClick()
                                    }
                                }
                            )
                        }
                    )
                }
            }
        }
    }
}

/**
 * 社交媒体风格的文本示例
 */
@Composable
fun SocialMediaTextExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            TitleText("社交媒体文本示例")
            
            // 模拟微博/Twitter 风格的文本
            val socialText = buildAppAnnotatedString {
                appendStyled(
                    text = "@张三",
                    color = Color(0xFF1DA1F2),
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    tag = "user_zhangsan"
                )
                append(" 刚刚分享了一个很棒的 ")
                appendStyled(
                    text = "#Android开发",
                    color = Color(0xFF1DA1F2),
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    tag = "hashtag_android"
                )
                append(" 教程！大家可以去 ")
                appendLink("这个链接", "https://example.com/tutorial")
                append(" 查看详情。")
                appendLine()
                appendLine()
                appendStyled(
                    text = "💡 小贴士：",
                    fontSize = 16.sp
                )
                append("记得点赞和转发哦～")
            }
            
            BodyText(
                text = socialText,
                onClick = { offset ->
                    AnnotatedStringUtils.handleClick(
                        annotatedString = socialText,
                        offset = offset,
                        onUrlClick = { url ->
                            // 打开链接
                        },
                        onUserClick = { userId ->
                            // 跳转到用户页面
                        },
                        onHashtagClick = { hashtag ->
                            // 搜索话题标签
                        }
                    )
                }
            )
        }
    }
}

@Preview(showBackground = true, name = "AnnotatedString 登录页面示例")
@Composable
private fun LoginScreenWithAnnotatedStringPreview() {
    AppTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            LoginScreenWithAnnotatedString()
            SocialMediaTextExample()
        }
    }
}
