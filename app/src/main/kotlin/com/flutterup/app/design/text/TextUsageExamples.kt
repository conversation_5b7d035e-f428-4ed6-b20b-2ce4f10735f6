package com.flutterup.app.design.text

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.design.theme.AppTheme

/**
 * 文字组件使用示例
 * 展示在实际场景中如何使用文字设计系统
 */

/**
 * 用户资料卡片示例
 */
@Composable
fun UserProfileCardExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 卡片标题
            CardTitle("用户信息")
            
            // 用户名
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                BodyText("用户名")
                EmphasisText("张三")
            }
            
            // 邮箱
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                BodyText("邮箱")
                BodyText("<EMAIL>")
            }
            
            // 注册时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                BodyText("注册时间")
                HintText("2024-01-01")
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 操作链接
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                LinkText("编辑资料")
                LinkText("修改密码")
                LinkText("注销账号")
            }
        }
    }
}

/**
 * 文章卡片示例
 */
@Composable
fun ArticleCardExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 文章标题
            CardTitle("如何构建优秀的 Android 应用")
            
            // 文章摘要
            CardSubtitle("本文将介绍如何使用现代 Android 开发技术栈构建高质量的移动应用，包括 Jetpack Compose、MVVM 架构模式等...")
            
            // 标签
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                TagText("Android")
                TagText("Compose")
                TagText("MVVM")
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 文章信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    HintText("作者：李四")
                    HintText("发布时间：2024-01-15")
                }
                
                Column(horizontalAlignment = Alignment.End) {
                    NumberText("1,234", size = NumberSize.Small)
                    HintText("阅读量")
                }
            }
        }
    }
}

/**
 * 表单示例
 */
@Composable
fun FormExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 表单标题
            CardTitle("个人信息设置")
            
            // 姓名输入
            Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                AppText(
                    text = "姓名 *",
                    style = TextPresets.inputLabel()
                )
                PlaceholderText("请输入您的真实姓名")
                HintText("姓名将用于身份验证")
            }
            
            // 邮箱输入
            Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                AppText(
                    text = "邮箱地址 *",
                    style = TextPresets.inputLabel()
                )
                PlaceholderText("<EMAIL>")
                ErrorText("请输入有效的邮箱地址")
            }
            
            // 电话输入
            Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                AppText(
                    text = "手机号码",
                    style = TextPresets.inputLabel()
                )
                PlaceholderText("请输入11位手机号码")
                HintText("用于接收重要通知")
            }
            
            // 提交按钮
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                ButtonText(
                    text = "保存设置",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onPrimary
                )
            }
        }
    }
}

/**
 * 统计数据展示示例
 */
@Composable
fun StatisticsExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 标题
            CardTitle("数据统计")
            
            // 统计数据
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    NumberText("1,234", size = NumberSize.Large)
                    HintText("总用户数")
                }
                
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    NumberText("567", size = NumberSize.Large)
                    HintText("活跃用户")
                }
                
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    NumberText("89", size = NumberSize.Large)
                    HintText("新增用户")
                }
            }
            
            // 增长率
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                EmphasisText("↑ 12.5%")
                EmphasisText("↑ 8.3%")
                EmphasisText("↑ 15.7%")
            }
        }
    }
}

/**
 * 导航菜单示例
 */
@Composable
fun NavigationMenuExample() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            CardTitle("导航菜单")
            
            // 主要导航
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                NavigationText("首页", selected = true)
                NavigationText("发现", selected = false)
                NavigationText("消息", selected = false)
                NavigationText("个人资料", selected = false)
            }
            
            // 分割线
            HintText("———————————————")
            
            // 次要导航
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                NavigationText("设置", selected = false)
                NavigationText("帮助", selected = false)
                NavigationText("关于", selected = false)
            }
        }
    }
}

@Preview(showBackground = true, name = "文字组件使用示例")
@Composable
private fun TextUsageExamplesPreview() {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            PageTitle("文字组件使用示例")
            
            UserProfileCardExample()
            ArticleCardExample()
            FormExample()
            StatisticsExample()
            NavigationMenuExample()
        }
    }
}
